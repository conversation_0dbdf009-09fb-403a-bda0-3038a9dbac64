
import React from 'react';

interface DoctorActivity {
  name: string;
  avgPatients: string | number;
  topDiagnosis: string;
  avgMeds: string | number;
}

interface DoctorActivityTableProps {
  data: DoctorActivity[];
}

const DoctorActivityTable: React.FC<DoctorActivityTableProps> = ({ data }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor Name</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Patients/Day</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Most Diagnosed</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Meds/Visit</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((doctor) => (
            <tr key={doctor.name} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{doctor.name}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{doctor.avgPatients}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                    {doctor.topDiagnosis}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{doctor.avgMeds}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DoctorActivityTable;
